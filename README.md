# 项目结构
techproclub/
├── app/
│   ├── app.vue                // 应用主入口，配置全局布局和页面元信息
│   ├── assets/                // 静态资源（如 svg 图标等）
│   ├── auto-imports.d.ts      // 自动导入类型声明（由插件生成）
│   ├── components/            // 全局/局部 Vue 组件目录
│   │   ├── AppFooter.vue      // 页脚组件
│   │   ├── AppHeader.vue      // 页头组件
│   ├── components.d.ts        // 组件自动导入类型声明（由插件生成）
│   ├── layouts/
│   │   └── default.vue        // 默认布局文件
│   ├── pages/
│   │   ├── about.vue          // “关于我们”页面
│   │   └── index.vue          // 首页
│   ├── plugins/               // Nuxt 插件目录（当前为空）
│   ├── styles/
│   │   └── style.less         // 全局样式（Less 预处理器）
│   └── utils/                 // 工具函数目录
├── nuxt.config.js             // Nuxt 配置文件，包含 SSR、样式、Vite、Ant Design Vue 等配置
├── package.json               // 项目依赖、脚本等基本信息
├── package-lock.json          // 依赖锁定文件
├── public/
│   ├── favicon.ico            // 网站图标
│   └── robots.txt             // 搜索引擎爬虫规则
├── README.md                  // 项目说明（英文，已补充中文说明）
└── tsconfig.json              // TypeScript 配置文件


# Nuxt Minimal Starter

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

- 运行环境：node 20.16

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
