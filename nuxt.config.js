import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import path from "path";
import {AntDesignVueResolver} from "unplugin-vue-components/resolvers";

export default defineNuxtConfig({
    compatibilityDate: "2025-08-08",

    // 仅在开发/调试时保留 server sourcemap
    sourcemap: {server: true, client: false},

    modules: ["@nuxtjs/i18n", "nuxt-swiper", "@nuxt/image"],

    ssr: true,
    srcDir: "./app",

    dir: {
        public: "public",
    },

    alias: {
        "@": "./app",
        "@/axios": "./app/axios/index",
        "@/components": "./app/components/",
        "@/utils": "./app/utils/"
    },

    css: ["@/styles/style.less", "@/styles/style1.less"],

    image: {
        allowAllPaths: true,
        presets: {
            webp: {
                modifiers: {
                    format: 'webp',
                    quality: 80 // 降一点质量，减小图片体积
                },
            }
        },
        format: ['webp'],
        provider: 'ipx',
        ipx: {
            modifiers: {
                quality: 90
            },
            maxAge: 86400 * 30  //24h
        }
    },

    experimental: {
        renderJsonPayloads: false,
        payloadExtraction: true, // 提取 payload.json 减少 HTML 体积
        inlineSSRStyles: false    // 避免内联 CSS，减少 HTML 首屏大小
    },

    app: {
        buildAssetsDir: "/_nuxt/",
        head: {
            title: "CRM",
            meta: [
                {name: "description", content: "CRM"},
                {name: "keyword", content: "CRM"},
                {name: "build_time", content: new Date().toISOString()},
                {name: "author", content: "CAY"},
                {name: "copyright", content: "(c) released under the all"},
            ],
            viewport: "width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0",
            charset: "utf-8",
            htmlAttrs: {
                lang: "zh-hk",
            },
            link: [
                {rel: "icon", type: "image/x-icon", href: "/favicon.ico"},
                {rel: "preconnect", href: "https://fonts.googleapis.com"},
                {rel: "preconnect", href: "https://fonts.gstatic.com", crossorigin: ""}
            ],
        },
    },

    vite: {
        plugins: [
            // SVG inline loader
            {
                name: "svg-sprite",
                configureServer(server) {
                    server.middlewares.use((req, res, next) => {
                        if (req.url.endsWith(".svg")) {
                            res.setHeader("Content-Type", "image/svg+xml");
                        }
                        next();
                    });
                },
                transform(code, id) {
                    if (id.endsWith(".svg")) {
                        return {
                            code: `export default ${JSON.stringify(code)}`,
                            map: null,
                        };
                    }
                },
            },
            AutoImport({
                imports: [
                    {
                        "ant-design-vue": ["message", "notification", "Modal"],
                    },
                ],
            }),
            Components({
                dirs: ["~/components"],
                extensions: ["vue"],
                include: [/\.vue$/, /\.vue\?vue/],
                resolvers: [
                    AntDesignVueResolver({
                        importStyle: "less",
                        resolveIcons: true,
                    }),
                ],
            }),
        ],

        ssr: {
            noExternal: ["ant-design-vue", "@ant-design/icons-vue"],
        },

        css: {
            preprocessorOptions: {
                less: {
                    javascriptEnabled: true,
                },
            },
        },

        build: {
            target: "esnext", // 输出现代 JS
            minify: "terser",
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true,
                },
                format: {
                    comments: false,
                },
            },
            rollupOptions: {
                output: {
                    chunkFileNames: `_nuxt/js/[hash].js`,
                    entryFileNames: `_nuxt/js/[hash].js`,
                    assetFileNames: `_nuxt/[ext]/[hash].[ext]`,
                    manualChunks: {
                        vue: ["vue", "vue-router"], // 核心框架单独打包
                        antd: ["ant-design-vue"],           // UI 库单独打包
                    },
                },
            },
        },
    },

    i18n: {
        defaultLocale: "zh-hk",
        detectBrowserLanguage: false,
        locales: [
            {code: "en", name: "English", file: "en.json"},
            {code: "zh-hk", name: "中文繁体", file: "zh-hk.json"},
            {code: "zh-cn", name: "中文简体", file: "zh-cn.json"},
        ],
    },

    swiper: {
        prefix: "Swiper",
        styleLang: "css",
        modules: ["navigation", "pagination", "autoplay"],
    },

    nitro: {
        preset: "node-server",
        configLock: false,
        output: {
            publicDir: path.resolve(__dirname, "./build/public"),
            dir: path.resolve(__dirname, "./build"),
            serverDir: path.resolve(__dirname, "./build/server"),
        },
        compressPublicAssets: true // Gzip / Brotli 压缩
    },
});
