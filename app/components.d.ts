/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ArrowUpOutlined: typeof import('@ant-design/icons-vue')['ArrowUpOutlined']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
