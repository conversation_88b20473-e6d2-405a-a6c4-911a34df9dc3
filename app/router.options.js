// app/router.options.js
export default {
  scrollBehavior(to, from, savedPosition) {
    // 返回一个 Promise，等待 DOM 更新
    return new Promise((resolve) => {
      setTimeout(() => {
        const appMain = document.querySelector(".app-main");

        if (!appMain) {
          // 如果找不到容器，回退到默认行为
          resolve({ top: 0 });
          return;
        }

        // 处理保存的位置
        if (savedPosition) {
          appMain.scrollTop = savedPosition.top || 0;
          resolve(savedPosition);
          return;
        }

        // 处理锚点
        if (to.hash) {
          const element = document.querySelector(to.hash);
          if (element) {
            const top = element.offsetTop;
            appMain.scrollTop = top;
            resolve({ el: to.hash, top });
            return;
          }
        }

        // 默认滚动到顶部
        appMain.scrollTop = 0;
        resolve({ top: 0 });
      }, 100); // 延迟确保 DOM 已更新
    });
  },
};
