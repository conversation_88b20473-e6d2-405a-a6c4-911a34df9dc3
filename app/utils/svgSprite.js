import { loadAllSvgIcons } from "../assets/svg";

// 加载所有SVG图标
const icons = loadAllSvgIcons();

// 创建SVG Sprite
export const createSvgSprite = () => {
  // 确保在客户端环境
  if (typeof window === "undefined") return;

  // 检查是否已经存在sprite
  const existingSprite = document.getElementById("svg-sprite-container");
  if (existingSprite) return;

  const svgSprite = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "svg"
  );
  const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");

  svgSprite.setAttribute("aria-hidden", "true");
  svgSprite.setAttribute("id", "svg-sprite-container");
  svgSprite.style.position = "absolute";
  svgSprite.style.width = "0";
  svgSprite.style.height = "0";
  svgSprite.style.overflow = "hidden";

  icons.forEach(async (icon) => {
    try {
      const content = await icon.content();
      const symbolId = icon.name;
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(content, "image/svg+xml");
      const svgElement = svgDoc.documentElement;

      const symbol = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "symbol"
      );
      symbol.setAttribute("id", symbolId);
      symbol.setAttribute(
        "viewBox",
        svgElement.getAttribute("viewBox") || "0 0 1024 1024"
      );

      while (svgElement.firstChild) {
        symbol.appendChild(svgElement.firstChild);
      }

      defs.appendChild(symbol);
    } catch (error) {
      console.error(`Error loading SVG icon ${icon.name}:`, error);
    }
  });

  svgSprite.appendChild(defs);
  document.body.insertBefore(svgSprite, document.body.firstChild);
};
