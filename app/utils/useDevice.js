// composables/useDevice.js
import { useWindowSize } from "@vueuse/core";
import { computed } from "vue";

// 定义断点常量
export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  laptop: 1440,
  desktop: 1920,
};

export const useDevice = () => {
  const { width, height } = useWindowSize();

  // 设备类型
  const deviceType = computed(() => {
    if (width.value < BREAKPOINTS.mobile) return "mobile"; // 手机
    if (width.value < BREAKPOINTS.tablet) return "tablet"; // 平板
    if (width.value < BREAKPOINTS.laptop) return "laptop"; // 笔记本
    if (width.value < BREAKPOINTS.desktop) return "desktop"; // 台式机
    return "large"; // 大型设备
  });

  // 布尔判断
  const isMobile = computed(() => width.value < BREAKPOINTS.tablet);
  const isTablet = computed(
    () => width.value >= BREAKPOINTS.mobile && width.value < BREAKPOINTS.tablet
  );
  const isLaptop = computed(
    () => width.value >= BREAKPOINTS.tablet && width.value < BREAKPOINTS.laptop
  );
  const isDesktop = computed(() => width.value >= BREAKPOINTS.laptop);

  // 小于某个断点
  const isLessThan = (breakpoint) => {
    return computed(() => width.value < BREAKPOINTS[breakpoint]);
  };

  // 大于某个断点
  const isGreaterThan = (breakpoint) => {
    return computed(() => width.value >= BREAKPOINTS[breakpoint]);
  };

  // 在某个范围内
  const isBetween = (min, max) => {
    return computed(
      () => width.value >= BREAKPOINTS[min] && width.value < BREAKPOINTS[max]
    );
  };

  return {
    width: readonly(width),
    height: readonly(height),
    deviceType: readonly(deviceType),
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isLaptop: readonly(isLaptop),
    isDesktop: readonly(isDesktop),
    isLessThan,
    isGreaterThan,
    isBetween,
    BREAKPOINTS,
  };
};
