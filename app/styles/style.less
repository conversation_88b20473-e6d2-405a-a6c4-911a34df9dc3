@font-face {
  font-family: 'Roboto';
  src: url('../assets/fonts/Roboto-Regular.ttf') format('truetype');
  font-style: normal;
  unicode-range: U+0030-0039, U+0041-005A, U+0061-007A;

}

*,
*::before,
*::after {
  box-sizing: border-box;
}

// 重置
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: "Roboto", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
  "Helvetica Neue", Arial, sans-serif;
  font-size: 22px;
  overflow-x: hidden;
  color: #5f6368;
  letter-spacing: 2px; 
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 标题
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 500;
}

// 段落
p {
  margin: 0;
}

// 列表
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}



// 图片
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 表单
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
}

.pt-md {
  padding-top: 60px;
  padding-bottom: 60px;
}

.pl-md {
  padding-left: 10px;
}

.pb-md {
  padding-bottom: 70px;
}

.text-md{
  letter-spacing: 2px; 
}

.title-48 {
  font-family: "Roboto", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  "Helvetica Neue", Arial, sans-serif;
  font-weight: 700;
  font-size: 52px;
  color: #202124;
}


.title-32 {
  font-size: 36px;
}

.text-page{
  padding: 60px 0;
}

.text-content-wrapper{
  display: flex;
  gap: 80px;
  align-items: flex-start;
}
.text-config{
  line-height: 1.8;
  white-space: pre-line;
  color: #202124;
}

.text-paragraph{
  margin-bottom: 20px;
}
.text-subhead{
  font-size: 28px;
  font-weight: 700;
  color: #202124;
}
.text-config-title{
  font-family: "Roboto", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  "Helvetica Neue", Arial, sans-serif;
  font-weight: 700;
  font-size: 52px;
  color: #202124;
  margin-bottom: 50px;
}
.video-content{
  margin-top: 8px;
}

.type-area {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding-left: 20px;
  padding-right: 20px;
}

.app-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.app-header {
  position: fixed;
  top: 0;
  z-index:3;
  width: 100%;
  height: 80px;
  background-color: #fff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.02);
}

.app-main {
  position: absolute;
  top: 80px;
  bottom: 0;
  left: 0;
  right: 0;
  overflow-x: hidden;
  overflow-y: auto;

  
  // 滚动条轨道透明，只在hover时显示
  &::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }

  // 滚动条轨道背景
  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 4px;
  }

  // 滚动条滑块
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }

  // hover时显示滚动条
  &:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
  }

  // 滚动条滑块hover时加深
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }

  // 正在滚动时也显示滚动条
  &::-webkit-scrollbar-thumb:active {
    background-color: rgba(0, 0, 0, 0.4);
  }
}

.app-footer {
  width: 100%;
  // height: 524px;
  background-color: #0f1e31;
  color: #fff;
}

