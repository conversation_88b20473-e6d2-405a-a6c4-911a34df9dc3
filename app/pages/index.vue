<template>
  <div class="homePage">
    <Banner />
    <SolutionsModule />
    <CoreBusiness />
    <TechnicalAdvantages />
    <TechInnovation />
    <AboutModule />
    <Values />
    <Partner />
  </div>
</template>

<script setup>
// ======================= 页面数据 =====================
import Banner from "./index/Banner.vue"; // 轮播图
import SolutionsModule from "./index/SolutionsModule.vue";
import CoreBusiness from "./index/CoreBusiness.vue";
import TechnicalAdvantages from "./index/TechnicalAdvantages.vue";
import TechInnovation from "./index/TechInnovation.vue";
import AboutModule from "./index/AboutModule.vue";
import Values from "./index/Values.vue";
import Partner from "./index/Partner.vue";
// ======================= 国际化 =======================
const { t, locale, setLocale } = useI18n();
definePageMeta({
  title: "test",
});

// 页面元数据
useHead({
  title: t("homePage"), // 页面标题
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});
</script>

<style lang="less" scoped></style>
