<template>
  <main class="page">
    <!-- banner -->
    <section id="banner">
      <NuxtImg preset="webp" src="/img/about-1.png" class="banner-image" />
    </section>
    <!-- 关于我们 -->
    <section id="about" class="flex-col">
      <div class="flex-col self-center container">
        <div class="flex-col items-start">
          <span class="title">{{ $t("about.about.title") }}</span>
          <!-- <span class="mt-27 subtitle">{{ $t('about.about.subtitle') }}</span> -->
        </div>
        <span class="mt-40 text_1">{{ $t("about.about.text_1") }}</span>
      </div>
      <div class="mt-36 flex-col self-center container">
        <div class="flex-row self-start">
          <NuxtImg preset="webp" class="shrink-0 self-start image_2" src="/img/about-2.png" />
          <span class="shrink-0 text_2">{{ $t("about.about.text_2") }}</span>
          <NuxtImg preset="webp" class="shrink-0 self-start image_2 image_3" src="/img/about-3.png" />
          <span class="flex-1 self-center text_3">{{
            $t("about.about.text_3")
          }}</span>
        </div>
      </div>
    </section>
    <!-- 价值观 -->
    <section id="values" class="flex-col justify-center items-center">
      <div class="flex-col justify-end items-end container">
        <div class="flex-col section_1">
          <span class="self-start title text_1">{{
            $t("about.values.title")
          }}</span>
          <div class="mt-10 flex-col self-stretch group_1">
            <template v-for="(n, i) in 5">
              <span class="self-start text_2">{{
                $t("about.values.text_2[" + i + "]")
              }}</span>
              <span class="self-stretch text_3">{{
                $t("about.values.text_3[" + i + "]")
              }}</span>
            </template>
          </div>
        </div>
      </div>
    </section>
    <!-- 加入我们 -->
    <section id="join" class="flex-col">
      <div class="flex-col self-center container">
        <div class="flex-col items-start">
          <span class="title">{{ $t("about.join.title") }}</span>
          <span class="mt-27 subtitle">{{ $t("about.join.subtitle") }}</span>
        </div>
      </div>
      <div class="flex-col self-center container">
        <div v-for="(n, i) in 2" :key="i" class="mt-50 flex-row justify-between">
          <div class="flex-col section_1">
            <div class="text_1">
              {{ $t("about.join.text_1[" + 2 * i + "]") }}
            </div>
            <div class="text_2">
              {{ $t("about.join.text_2[" + 2 * i + "]") }}
            </div>
          </div>
          <div class="flex-col section_2">
            <div class="text_1">
              {{ $t("about.join.text_1[" + (2 * i + 1) + "]") }}
            </div>
            <div class="text_2">
              {{ $t("about.join.text_2[" + (2 * i + 1) + "]") }}
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 联系我们 -->
    <section id="contact" class="flex-col">
      <div class="flex-col self-center container">
        <div class="flex-col items-start">
          <span class="title">{{ $t("about.contact.title") }}</span>
          <!-- <span class="mt-27 subtitle">{{ $t('about.contact.subtitle') }}</span> -->
        </div>
      </div>
      <div class="mt-50 flex-col self-center container">
        <ClientOnly>
          <iframe v-if="useGoogle" id="map" :src="mapurl" style="border: 0" loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"></iframe>
          <a target="_blank" v-else="useGoogle"
            href="https://map.baidu.com/search/%E9%B4%BB%E5%9C%96%E9%81%931%E8%99%9F/@12716095.990745908,2533396.956060599,19z?querytype=s&da_src=shareurl&wd=%E9%B4%BB%E5%9C%96%E9%81%931%E8%99%9F&c=340&src=0&pn=0&sug=0&l=13&b=(12648061,2552312;12709501,2581464)&from=webmap&biz_forward=%7B%22scaler%22:1,%22styles%22:%22pl%22%7D&device_ratio=1">
            <NuxtImg preset="webp" src="/img/map.png" />
          </a>
        </ClientOnly>
      </div>
      <div class="flex-col self-center container">
        <div class="flex-row justify-between">
          <div class="flex-row items-center section_1">
            <SvgIcon iconFileName="icon_地址" width="48px" height="48px" />
            <div class="flex-col items-start ml-21">
              <span class="text_1">{{ $t("about.contact.text_1[0]") }}</span>
              <span class="text_2">{{ $t("about.contact.text_2[0]") }}</span>
            </div>
          </div>
          <div class="flex-row items-center section_2">
            <div>
              <SvgIcon iconFileName="icon_emall" width="48px" height="48px" />
            </div>
            <div class="flex-col items-start ml-21">
              <span class="text_1">{{ $t("about.contact.text_1[1]") }}</span>
              <span class="text_2">{{ $t("about.contact.text_2[1]") }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup name="about">
const { t, locale, setLocale } = useI18n();
useHead({
  title: t("home.header_list.about"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});

const mapurl_en =
  "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d29528.450543418065!2d114.20177546761329!3d22.313709747530318!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34040148fa17f347%3A0xc71232b210ddbbfc!2s1%20Hung%20To%20Road%2C%201%20Hung%20To%20Rd%2C%20Kwun%20Tong!5e0!3m2!1sen!2shk!4v1754380879577!5m2!1sen!2shk";
const mapurl_zh_hk =
  "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d35115.90006019387!2d114.20874018408284!3d22.311894483589693!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34040148fa17f347%3A0xc71232b210ddbbfc!2z6KeA5aGY6bS75ZyW6YGTMeiZn-m0u-WclumBkzHomZ8!5e0!3m2!1szh-TW!2shk!4v1754381212043!5m2!1szh-TW!2shk";
const mapurl = locale.value == "en" ? mapurl_en : mapurl_zh_hk;
const useGoogle = ref(false);

onMounted(async () => {
  try {
    await fetch(mapurl, { method: "GET" });
    useGoogle.value = true;
  } catch (error) {
    useGoogle.value = false;
  }
});
</script>

<style scoped lang="less">
.page {
  .image_2 {
    width: 9px;
    height: 9px;
    margin-left: 6px;
    margin-top: 10px;
  }

  .image_3 {
    margin-left: -4px;
    margin-top: 34px;
  }

  #about {
    padding-top: 60px;

    .text_1 {
      line-height: 1.8;
      text-align: justify;
      color: #202124;
    }

    .text_2 {
      margin-top: 3px;
      font-size: 24px;
      color: #202124;
      line-height: 1.8;
      font-weight: 700;
    }

    .text_3 {
      margin-left: 9px;
      line-height: 1.8;
      text-align: justify;
      color: #202124;
    }
  }

  #values {
    margin-top: 60px;
    background-image: url("/_ipx/f_webp&q_90/images/核心价值观_bg.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 800px;

    .section_1 {
      padding: 80px;
      background-color: #1a73e8b3;
      width: 900px;
      color: #fff;

      .text_1 {
        font-size: 36px;
        font-weight: normal;
        color: #fff;
      }

      .text_2 {
        font-size: 36px;
        margin-top: 30px;
      }

      .text_3 {
        margin-top: 22px;
        line-height: 1.6;
      }
    }
  }

  #join {
    padding-top: 60px;
    background-image: linear-gradient(178.8deg, #eff3fd 0.9%, #ffffff 97.6%);

    .section_1 {
      margin-right: 60px;
      width: 50%;
    }

    .section_2 {
      margin-left: 6rem;
      width: 50%;
    }

    .text_1 {
      padding: 1rem 0;
      font-size: 36px;
      line-height: 2;
      font-weight: 700;
      border-bottom: 1px solid #ddd;
      color: #202124;
    }

    .text_2 {
      margin-top: 30px;
      line-height: 2;
    }
  }

  #contact {
    padding-top: 60px;

    #map {
      width: 100%;
      height: 18rem;
    }

    .section_1,
    .section_2 {
      width: 42%;
      padding: 36px 0;
      margin-bottom: 72px;
      border-bottom: 1px solid #ddd;
      color: #36383d;

      .text_1 {
        font-size: 20px;
        color: #5f6368;
        line-height: 2;
      }

      .text_2 {
        font-size: 20px;
        line-height: 2;
        color: #202124;
      }
    }
  }
}
</style>
