<template>
  <!-- 橋隧綜合评估 -->
  <div class="bridge-assess text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title6") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="bridge-assess" />
        </div>
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.bridgeAssess_content1") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.bridgeAssess_content2") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="BridgeAssess">
import { ref } from "vue";

import img1 from "/images/解决方案/img1-橋隧綜合评估.png";
import img2 from "/images/解决方案/img2-橋隧綜合评估.png";
import img3 from "/images/解决方案/img3-橋隧綜合评估.png";

const slides = ref([
  {
    type: "image",
    src: img1,
    alt: "橋隧綜合评估",
  },
  {
    type: "image",
    src: img2,
    alt: "橋隧綜合评估-2",
  },
  {
    type: "image",
    src: img3,
    alt: "橋隧綜合评估-3",
  },
]);
</script>

<style lang="less" scoped>
.bridge-assess {
  background: #fff;
  .text-config-title {
    width: 100%;
    margin-left: calc(50% - 60px);
  }
  .textpage-content {
    width: 60%;
    padding-right: 20px;
  }

  .video-content {
    width: 40%;
  }
}
</style>
