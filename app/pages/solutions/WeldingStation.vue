<template>
  <!-- 协作机器人焊接工作站 -->
  <div class="welding-station text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title2") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="welding-station" />
        </div>
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.weldingStation_content") }}
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.weldingStation_feature1_title") }}
            </div>
            <span>
              {{ $t("solutions.weldingStation_feature1_content") }}
            </span>
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.weldingStation_feature2_title") }}
            </div>
            <span>{{ $t("solutions.weldingStation_feature2_content") }}</span>
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.weldingStation_feature3_title") }}
            </div>
            <span>
              {{ $t("solutions.weldingStation_feature3_content") }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="WeldingStation">
import { ref } from "vue";

import cleanerImg from "/images/解决方案/img1-協作機器人焊接工作站.png";
import cleanerImg2 from "/images/解决方案/img2-協作機器人焊接工作站.png";
import cleanerImg3 from "/images/解决方案/img3-協作機器人焊接工作站.png";

const slides = ref([
  {
    type: "image",
    src: cleanerImg,
    alt: "协作机器人焊接工作站",
  },
  {
    type: "image",
    src: cleanerImg2,
    alt: "协作机器人焊接工作站-2",
  },
  {
    type: "image",
    src: cleanerImg3,
    alt: "协作机器人焊接工作站-3",
  },
]);
</script>

<style lang="less" scoped>
.welding-station {
  background: #fff;

  .text-config-title {
    width: 100%;
    margin-left: calc(50% - 60px) !important;
  }
  .textpage-content {
    width: 60%;
  }

  .video-content {
    width: 40%;
  }
}
</style>
