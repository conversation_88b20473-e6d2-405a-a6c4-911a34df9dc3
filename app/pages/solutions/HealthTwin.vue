<template>
  <!-- 建筑、桥梁结构健康监测孪生系统 -->
  <div class="health-twin text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title4") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="health-twin" />
        </div>
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            {{ $t("solutions.healthTwin_content1") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.healthTwin_content2") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.healthTwin_content3") }}
          </div>
          <div class="text-paragraph">
            {{ $t("solutions.healthTwin_content4") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="HealthTwin">
import { ref } from "vue";

import video from "/videos/人行天桥视频_水印_x264.mp4";
import img2 from "/images/解决方案/img2-結構健康監測孿生系统.png";
import img3 from "/images/解决方案/img3-結構健康監測孿生系统.png";
import img4 from "/images/解决方案/img4-結構健康監測孿生系统.png";
import img5 from "/images/解决方案/img5-結構健康監測孿生系统.png";
import img6 from "/images/解决方案/img6-結構健康監測孿生系统.png";

const slides = ref([
  {
    type: "video",
    src: video,
    alt: "人行天桥视频",
  },
  {
    type: "image",
    src: img2,
    alt: "結構健康監測孿生系统-2",
  },
  {
    type: "image",
    src: img3,
    alt: "結構健康監測孿生系统-3",
  },
  {
    type: "image",
    src: img4,
    alt: "結構健康監測孿生系统-4",
  },
  {
    type: "image",
    src: img5,
    alt: "結構健康監測孿生系统-5",
  },
  {
    type: "image",
    src: img6,
    alt: "結構健康監測孿生系统-6",
  },
]);
</script>

<style lang="less" scoped>
.health-twin {
  background: #fff;

  .text-config-title {
    width: 100%;
    margin-left: calc(50% - 60px);
  }
  .textpage-content {
    width: 60%;
    padding-right: 20px;
  }

  .video-content {
    width: 40%;
  }
}
</style>
