<template>
  <!-- 协作机器人焊接工作站 -->
  <div class="bridge text-page">
    <div class="container type-area">
      <div class="text-config-title">
        {{ $t("solutions.solutions_title3") }}
      </div>
      <div class="text-paragraph text-config textTop">
        {{ $t("solutions.bridgeDrone_content1") }}
      </div>
      <div class="text-content-wrapper">
        <!-- 文字 -->
        <div class="textpage-content text-config">
          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.bridgeDrone_feature1_title") }}
            </div>
            <span>
              {{ $t("solutions.bridgeDrone_feature1_content") }}
            </span>
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.bridgeDrone_feature2_title") }}
            </div>
            <span>{{ $t("solutions.bridgeDrone_feature2_content") }}</span>
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.bridgeDrone_feature3_title") }}
            </div>
            <span>
              {{ $t("solutions.bridgeDrone_feature3_content") }}
            </span>
          </div>

          <div class="text-paragraph">
            <div class="text-subhead">
              {{ $t("solutions.bridgeDrone_feature4_title") }}
            </div>
            <span>
              {{ $t("solutions.bridgeDrone_feature4_content") }}
            </span>
          </div>
        </div>
        <!-- 轮播 -->
        <div class="video-content">
          <CustomSwiper :slides="slides" custom-class="bridge-drone" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="BridgeDrone">
import { ref } from "vue";
import video from "/videos/基于Slam的桥梁巡检.mp4";
import img2 from "/images/解决方案/img2-橋梁專業無人機.png";
import img3 from "/images/解决方案/img3-橋梁專業無人機.png";
import img4 from "/images/解决方案/img4-橋梁專業無人機.png";

const slides = ref([
  {
    type: "video",
    src: video,
    alt: "基于Slam的桥梁巡检",
  },
  {
    type: "image",
    src: img2,
    alt: "橋梁專業無人機-2",
  },
  {
    type: "image",
    src: img3,
    alt: "橋梁專業無人機-3",
  },
  {
    type: "image",
    src: img4,
    alt: "橋梁專業無人機-4",
  },
]);
</script>

<style lang="less" scoped>
.bridge {
  background: linear-gradient(180deg, #f2f6ff 0%, #ffffff 100%);

  .text-content-wrapper {
    display: flex;
    gap: 80px;
    align-items: center;
  }

  .textTop {
    margin-bottom: 40px;
  }

  .textpage-content {
    width: 60%;
  }

  .video-content {
    width: 40%;
  }
}
</style>
