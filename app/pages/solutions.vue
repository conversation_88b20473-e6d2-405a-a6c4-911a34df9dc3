<template>
  <ScrollSection :items="navItems" />
</template>

<script setup name="Solutions">
import { ref } from "vue";
import { markRaw } from "vue";

import TunnelCleaner from "./solutions/TunnelCleaner.vue";
import WeldingStation from "./solutions/WeldingStation.vue";
import BridgeDrone from "./solutions/BridgeDrone.vue";
import HealthTwin from "./solutions/HealthTwin.vue";
import VesselMonitor from "./solutions/VesselMonitor.vue";
import BridgeAssess from "./solutions/BridgeAssess.vue";
import TDMeasurement from "./solutions/TDMeasurement.vue";

const navItems = ref([
  {
    title: "solutions.solutions_title1",
    component: markRaw(TunnelCleaner),
  },
  {
    title: "solutions.solutions_title2",
    component: markRaw(WeldingStation),
  },
  {
    title: "solutions.solutions_title3",
    component: markRaw(BridgeDrone),
  },
  {
    title: "solutions.solutions_title4",
    component: markRaw(HealthTwin),
  },
  {
    title: "solutions.solutions_title5",
    component: markRaw(VesselMonitor),
  },
  {
    title: "solutions.solutions_title6",
    component: markRaw(BridgeAssess),
  },
  {
    title: "solutions.solutions_title7",
    component: markRaw(TDMeasurement),
  },
]);

const { t, locale, setLocale } = useI18n();

useHead({
  title: t("solutionsPage"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});
</script>

<style lang="less" scoped></style>
