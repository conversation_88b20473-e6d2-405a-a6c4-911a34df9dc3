<template>
  <a-carousel autoplay arrows class="banner-carousel">
    <template #prevArrow>
      <div class="custom-slick-arrow" style="left: 200px; z-index: 1">
        <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
      </div>
    </template>
    <template #nextArrow>
      <div class="custom-slick-arrow" style="right: 200px">
        <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
      </div>
    </template>
    <div class="banner-item">
      <h1>{{ $t("home.banner_title") }}</h1>
    </div>
    <div class="banner-item">
      <h1>{{ $t("home.banner_title") }}</h1>
    </div>
    <div class="banner-item">
      <h1>{{ $t("home.banner_title") }}</h1>
    </div>
  </a-carousel>
</template>
<script lang="ts" setup></script>
<style lang="less" scoped>
:deep(.slick-slide) {
  text-align: center;
  height: 600px;
  line-height: 600px;
  background: #364d79;
  overflow: hidden;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 48px;
  height: 48px;
  font-size: 52px;
  border-radius: 50%;
  transition: ease all 0.3s;
  z-index: 1;
  color: #fff;
  opacity: 0;
  top: calc(50% - 24px);
}

.banner-carousel:hover {
  :deep(.slick-arrow.custom-slick-arrow) {
    opacity: 1;
  }
}

:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}
:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.slick-slide h1) {
  color: #fff;
  font-size: 84px;
  letter-spacing: 8px;
}

.banner-item {
  background: url("/_ipx/f_webp&q_90/images/首页banner.png") no-repeat center
    center;
  background-size: cover;
}
</style>
