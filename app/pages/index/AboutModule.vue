<template>
  <!-- 關於特科普 -->
  <div class="aboutModule type-area pb-md">
    <TitleBox
      :title="$t('home.aboutModule_title')"
      :subTitle="$t('home.aboutModule_subTitle')"
    />

    <div class="about-content">
      <div>{{ $t("home.aboutModule_content01") }}</div>
      <div class="vision-wrapper">
        <div class="vision-title"></div>
        <div class="vision-content">
          <span class="vision-title">
            <SvgIcon
              iconFileName="icon_愿景-left"
              width="0.5em"
              height="0.5em" />
            <span>{{ $t("home.aboutModule_cnt02") }}</span>
            <SvgIcon
              iconFileName="icon_愿景-right"
              width="0.5em"
              height="0.5em"
          /></span>
          {{ $t("home.aboutModule_cnt03") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="AboutModule"></script>

<style lang="less" scoped>
.aboutModule {
  margin-bottom: 100px;
  .about-content {
    div {
      margin-bottom: 40px;
      line-height: 1.8;

      &:last-child {
        margin-bottom: 0;
      }
    }
    color: #202124;

    .vision-wrapper {
      display: flex;
      align-items: baseline;
      margin-bottom: 0;
      white-space: nowrap;
      flex-wrap: nowrap;
      width: 100%;

      .vision-title {
        display: inline-flex;
        align-items: center;
        position: relative;
        margin: 0 12px 0 0;
        white-space: nowrap;
        line-height: 1.8;

        :deep(svg) {
          position: absolute;

          &:first-child {
            left: -6px;
            top: 4px;
          }

          &:last-child {
            right: -6px;
            bottom: 4px;
          }
        }

        span {
          font-size: 24px;
          font-weight: 700;

          white-space: nowrap;
        }
      }

      .vision-content {
        margin-bottom: 0;
        white-space: normal;
        flex: 1;
        line-height: 1.8;
      }
    }
  }
}
</style>
