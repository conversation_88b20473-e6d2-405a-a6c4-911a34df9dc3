<template>
  <!-- 核心价值观 -->
  <div class="valuesBox">
    <div class="values-content">
      <div class="values-title">{{ $t("home.values_title") }}</div>
      <div class="values-list">
        <div
          class="value-item"
          v-for="(item, index) in valuesList"
          :key="index"
        >
          <div class="value-title">
            <span>{{ index + 1 }}/</span
            >{{ $t(`home.values_list.${item.title}`) }}
          </div>
          <div class="value-desc">
            {{ $t(`home.values_list.${item.content}`) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Values">
const valuesList = [
  {
    title: "tit1",
    content: "content1",
  },
  {
    title: "tit2",
    content: "content2",
  },
  {
    title: "tit3",
    content: "content3",
  },
  {
    title: "tit4",
    content: "content4",
  },
  {
    title: "tit5",
    content: "content5",
  },
];
</script>

<style lang="less" scoped>
.valuesBox {
  min-height: 800px;
  margin-bottom: 70px;
  background-image: url("/_ipx/f_webp&q_90/images/核心价值观_bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .values-content {
    width: 800px;
    margin-right: 25%;
    background-color: rgba(26, 115, 232, 0.7);
    padding: 50px;
    color: #fff;

    .values-title {
      font-size: 36px;
      margin-bottom: 16px;
    }
    .values-list {
      .value-item {
        margin-bottom: 30px;

        &:last-child {
          margin-bottom: 0;
        }

        .value-title {
          font-size: 36px;
          margin-bottom: 16px;

          span {
            margin-right: 8px;
          }
        }

        .value-desc {
          font-size: 22px;
          line-height: 1.6;
        }
      }
    }
  }
}
</style>
