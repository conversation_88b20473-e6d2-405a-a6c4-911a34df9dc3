<template>
  <!-- 核心业务 -->
  <div class="coreBusiness type-area pb-md">
    <TitleBox
      :title="$t('home.coreBusiness_title')"
      :subTitle="$t('home.coreBusiness_subTitle')"
      className="pl-md"
    />
    <div class="swiper-container">
      <Swiper
        :modules="modules"
        :slides-per-view="3"
        :slides-per-group="1"
        :loop="true"
        :navigation="{
          nextEl: '.core-business-button-next',
          prevEl: '.core-business-button-prev',
        }"
        :space-between="40"
        :grid="{
          rows: 1,
        }"
      >
        <SwiperSlide v-for="(slide, index) in slides" :key="index">
          <div class="slide-item" @click="onSlideClick(slide)">
            <div class="slide-content">
              <NuxtImg preset="webp" class="slide-img" :src="slide.img" />
              <div class="slide-title">
                <span class="sub-tit title-32"
                  >{{ $t(`home.coreBusiness_list.${slide.title}`) }}
                </span>
                <span class="sub-tit sub-title">
                  {{ $t(`home.coreBusiness_list.${slide.subTitle}`) }}
                </span>
                <span class="sub-tit sub-btn">
                  {{ $t("home.coreBusiness_button") }}
                  <SvgIcon iconFileName="箭头" width="24px" height="24px" />
                </span>
              </div>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>

      <div class="swiper-button-prev custom-nav-btn core-business-button-prev">
        <SvgIcon iconFileName="banner-left" width="1em" height="1em" />
      </div>
      <div class="swiper-button-next custom-nav-btn core-business-button-next">
        <SvgIcon iconFileName="banner-right" width="1em" height="1em" />
      </div>
    </div>
  </div>
</template>

<script setup name="CoreBusiness">
import { ref } from "vue";
import { ArrowUpOutlined } from "@ant-design/icons-vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination } from "swiper/modules";
import { goTo } from "../../utils/goTo";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import img1 from "/images/核心业务-1.png";
import img2 from "/images/核心业务-2.png";
import img3 from "/images/核心业务-3.png";
import img4 from "/images/核心业务-4.png";

const modules = [Navigation, Pagination];

const slides = ref([
  { title: "tit1", img: img1, subTitle: "subTitle1", path: "/robots" },
  { title: "tit2", img: img2, subTitle: "subTitle2", path: "/algorithms" },
  { title: "tit3", img: img3, subTitle: "subTitle3", path: "/analysis" },
  { title: "tit4", img: img4, subTitle: "subTitle4", path: "/monitoring" },
]);

const { locale } = useI18n();
const onSlideClick = async (slide) => {
  await goTo(locale.value, slide.path);
};
</script>

<style lang="less" scoped>
.swiper-container {
  position: relative;
}
.slide-item {
  user-select: none;
  position: relative;
  font-size: 24px;
  color: #fff;
  cursor: pointer;
  height: 620px;
  padding: 10px;
  .slide-content {
    width: 100%;
    height: 95%;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    align-items: center;

    .slide-img {
      width: 100%;
      height: 60%;
      object-fit: cover;
      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-title {
      flex: 1;
      width: 100%;
      color: #202124;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      padding: 20px;
      justify-content: space-between;

      .sub-title {
        color: #5f6368;
        font-size: 22px;
        text-align: left;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        margin: 10px 0 0 0;
      }

      .sub-btn {
        color: #1a73e8;
        font-size: 22px;
        text-align: left;
        display: flex;
        align-items: center;
        margin: 10px 0 10px 0;
      }
    }
  }

  &:hover {
    .slide-content {
      transform: translateY(-8px);
      box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    .slide-title {
      background: linear-gradient(272.4deg, #1a73e8 2.01%, #1fb4ff 98.81%);
    }
    .sub-tit {
      color: #fff !important;
    }
  }
}

.custom-nav-btn {
  position: absolute;
  width: 48px;
  height: 48px;
  cursor: pointer;
  color: #a8b0bb;
  font-size: 52px;
  border-radius: 50%;
  &:hover {
    color: rgba(26, 115, 232, 1);
    background-color: rgba(26, 115, 232, 0.2);
    border-radius: 50%;
  }

  &.swiper-button-prev {
    left: -70px;
  }
  &.swiper-button-next {
    right: -70px;
  }
}
:deep(.swiper-button-prev),
:deep(.swiper-button-next) {
  &::after {
    display: none !important;
  }
}
</style>
