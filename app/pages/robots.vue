<template>
  <!-- 智能機器人 -->
  <div class="RobotsPage">
    <div class="firstImg">
      <div class="firstImg-text">{{ $t("robots.firstImg") }}</div>
    </div>
    <div class="robots-content">
      <div class="basicModel text-page type-area">
        <div class="text-content-wrapper">
          <div class="img-wrapper">
            <NuxtImg preset="webp" :src="robotImg" alt="" />
          </div>

          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("robots.basicModel") }}
            </div>
            <div class="text-config">
              {{ $t("robots.basicModel_content") }}
            </div>

            <div class="text-paragraph text-config">
              {{ $t("robots.basicModel_content2") }}
            </div>
          </div>
        </div>
      </div>

      <div class="nicholasBlack text-page type-area">
        <div class="text-content-wrapper">
          <div class="textpage-content">
            <div class="text-config-title">
              {{ $t("robots.nicholasBlack") }}
            </div>
            <div class="text-paragraph text-config">
              {{ $t("robots.nicholasBlack_content") }}
            </div>
          </div>

          <div class="img-wrapper nicholasBlack-img">
            <div class="nicholas-text-wrapper" v-for="item in nicholasList">
              <NuxtImg preset="webp" :src="item.src" :alt="item.alt" />
              <span class="nicholas-text"
                >{{ $t(`robots.${item.i18Tit}`) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Robots">
const { t, locale, setLocale } = useI18n();
import robotImg from "/images/解决方案/img2-隧道智能清洗機器人.png";

import img1 from "/images/机器人无人机/打磨.png";
import img2 from "/images/机器人无人机/焊接.png";
import img3 from "/images/机器人无人机/噴塗.png";
import img4 from "/images/机器人无人机/視覺檢測.png";
import img5 from "/images/机器人无人机/抓取移動.png";
import img6 from "/images/机器人无人机/安裝固定.png";

const nicholasList = ref([
  {
    src: img1,
    alt: "打磨",
    i18Tit: "nicholas01",
  },
  {
    src: img2,
    alt: "焊接",
    i18Tit: "nicholas02",
  },
  {
    src: img3,
    alt: "噴塗",
    i18Tit: "nicholas03",
  },
  {
    src: img4,
    alt: "視覺檢測",
    i18Tit: "nicholas04",
  },
  {
    src: img5,
    alt: "抓取移動",
    i18Tit: "nicholas05",
  },
  {
    src: img6,
    alt: "安裝固定",
    i18Tit: "nicholas06",
  },
]);

useHead({
  title: t("robotsPage"),
  meta: [
    { name: "description", content: "我们提供专业的技术服务和创新解决方案" },
  ],
});
</script>

<style lang="less" scoped>
.RobotsPage {
  background-color: #f8faff;
}
.firstImg {
  height: 480px;
  background-image: url("/_ipx/f_webp&q_90/images/机器人无人机/隧道智能清洗机器人-banner.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  position: relative;
  .firstImg-text {
    width: 780px;
    font-size: 24px;
    line-height: 2;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-13%, -50%);
  }
}

.robots-content {
  .textpage-content {
    width: 60%;
  }
  padding: 30px 0;
  .text-content-wrapper {
    justify-content: space-between;
  }

  .img-wrapper {
    img {
      border-radius: 16px;
      width: 556px !important;
      height: 334px !important;
      object-fit: cover;
    }
  }
  .text-config-title {
    margin-bottom: 30px;
  }
}

.nicholasBlack {
  margin-top: 30px;
  .textpage-content {
    width: 38%;
  }

  .img-wrapper {
    width: 62%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;

    .nicholas-text-wrapper {
      position: relative;
      width: 100%;
      border-radius: 8px;
      box-shadow: 0px 8px 12px rgba(0, 0, 0, 0.04);
      border-radius: 4px;
      background-color: #fff;
    }

    img {
      width: 268px !important;
      height: 226px !important;
      object-fit: cover;
      border-radius: 4px !important;
    }

    .nicholas-text {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      color: #020107;
      font-size: 20px;
    }
  }
}
</style>
