<template>
  <div class="scroll-section-box">
    <ClientOnly>
      <Pagination
        v-show="showPagination"
        :items="items"
        :current-section="currentSection"
        @change="handleSectionChange"
      />
    </ClientOnly>

    <!-- 内容区域 -->
    <div class="sections-container">
      <div
        v-for="(item, index) in items"
        :key="index"
        :id="`section-${index}`"
        class="section"
        :data-section-index="index"
      >
        <component :is="item.component" />
      </div>
    </div>
  </div>
</template>

<script setup name="ScrollSection">
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import Pagination from "./Pagination.vue";

const props = defineProps({
  items: {
    type: Array,
    required: true,
    default: () => [],
  },
  footerHeight: {
    type: Number,
    default: 524,
  },
  scrollOffset: {
    type: Number,
    default: 400,
  },
});

const currentSection = ref(0);
const showPagination = ref(true);
let scrollHandler = null;
let isScrolling = false;

// 处理区域变化
const handleSectionChange = (index) => {
  currentSection.value = index;
  isScrolling = true; // 标记正在进行程序化滚动
  scrollToSection(index);
};

// 滚动到指定区域
const scrollToSection = async (index) => {
  await nextTick();

  const section = document.querySelector(`[data-section-index="${index}"]`);
  const appMain = document.querySelector(".app-main");

  if (section && appMain) {
    const appMainRect = appMain.getBoundingClientRect();
    const sectionRect = section.getBoundingClientRect();
    const relativeTop = sectionRect.top - appMainRect.top + appMain.scrollTop;
    const top = relativeTop - 80;

    appMain.scrollTo({
      top,
      behavior: "smooth",
    });

    // 滚动结束后重置标记
    setTimeout(() => {
      isScrolling = false;
    }, 500);
  }
};

// 使用滚动监听来判断当前区域
const handleScroll = () => {
  if (isScrolling) return; // 如果是程序化滚动，不处理
  const appMain = document.querySelector(".app-main");
  if (!appMain) return;
  const scrollTop = appMain.scrollTop;
  const appMainHeight = appMain.clientHeight;
  const sections = document.querySelectorAll(".section[data-section-index]");

  // 检查是否在顶部
  showPagination.value = scrollTop > 100;

  // 检查是否滚动到底部（考虑 footer）
  const scrollHeight = appMain.scrollHeight;
  const isNearBottom =
    scrollTop + appMainHeight >= scrollHeight - props.footerHeight;

  if (isNearBottom) {
    currentSection.value = props.items.length - 1;
    return;
  }

  // 找出当前应该激活的 section
  let activeIndex = 0;

  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];
    const rect = section.getBoundingClientRect();
    const appMainRect = appMain.getBoundingClientRect();
    const sectionTop = rect.top - appMainRect.top + scrollTop;
    const sectionBottom = sectionTop + rect.height;

    // 判断当前滚动位置是否在这个 section 的范围内
    if (
      scrollTop + props.scrollOffset >= sectionTop &&
      scrollTop + props.scrollOffset < sectionBottom
    ) {
      activeIndex = i;
      break;
    }

    // 如果滚动位置超过了 section 的中点，也认为是当前 section
    const sectionMiddle = sectionTop + rect.height / 2;
    if (scrollTop + appMainHeight / 2 >= sectionMiddle) {
      activeIndex = i;
    }
  }

  if (currentSection.value !== activeIndex) {
    currentSection.value = activeIndex;
  }
};

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 创建防抖后的滚动处理函数
const debouncedHandleScroll = debounce(handleScroll, 100);

// 初始化滚动监听
const initScrollListener = () => {
  const appMain = document.querySelector(".app-main");
  if (appMain) {
    scrollHandler = debouncedHandleScroll;
    appMain.addEventListener("scroll", scrollHandler);
  }
};

onMounted(() => {
  if (process.client) {
    nextTick(() => {
      initScrollListener();
      // 初始化时检查滚动位置
      const appMain = document.querySelector(".app-main");
      if (appMain) {
        showPagination.value = appMain.scrollTop > 100;
      }
    });
  }
});

onBeforeUnmount(() => {
  const appMain = document.querySelector(".app-main");
  if (appMain && scrollHandler) {
    appMain.removeEventListener("scroll", scrollHandler);
  }
});
</script>

<style lang="less" scoped>
.scroll-section-box {
  .sections-container {
    .section {
      min-height: 500px;
      &:nth-child(odd) {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
