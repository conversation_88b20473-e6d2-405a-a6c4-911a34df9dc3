<template>
  <div class="pagination-box">
    <div class="pagination-nav" :style="navStyle">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="nav-item"
        :class="{ active: currentSection === index }"
        :style="itemStyle"
        @click="handleClick(index)"
      >
        {{ $t(item.title) }}
      </div>
    </div>
  </div>
</template>

<script setup name="Pagination">
import { computed } from "vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  currentSection: {
    type: Number,
    default: 0,
  },
  height: {
    type: Number,
    default: 80,
  },
  activeColor: {
    type: String,
    default: "#1a73e8",
  },
  backgroundColor: {
    type: String,
    default: "#fff",
  },
});

const emit = defineEmits(["change"]);

// 计算导航栏样式
const navStyle = computed(() => ({
  height: `${props.height}px`,
  backgroundColor: props.backgroundColor,
}));

// 计算导航项样式
const itemStyle = computed(() => ({
  height: `${props.height}px`,
  lineHeight: `${props.height}px`,
}));

// 处理点击事件
const handleClick = (index) => {
  emit("change", index);
};
</script>

<style lang="less" scoped>
.pagination-box {
  .pagination-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.02);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;

    .nav-item {
      padding: 0 20px;
      cursor: pointer;
      transition: all 0.3s;
      margin: 0 10px;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        border-bottom: 2px solid v-bind(activeColor);
      }

      &.active {
        color: v-bind(activeColor);
        border-bottom: 2px solid v-bind(activeColor);
      }
    }
  }
}

@media (max-width: 2150px) {
  .pagination-box {
    .pagination-nav {
      .nav-item {
        padding: 0 10px;
        margin: 0 5px;
      }
    }
  }
}

@media (max-width: 1850px) {
  .pagination-box {
    .pagination-nav {
      .nav-item {
        padding: 0 5px;
        margin: 0 2px;
      }
    }
  }
}
</style>
