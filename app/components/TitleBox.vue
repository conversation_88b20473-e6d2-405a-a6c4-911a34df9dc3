<template>
  <div class="TitleBox pt-md" :class="className">
    <div class="title-48">{{ title }}</div>
    <!-- <div class="subTitle">{{ subTitle }}</div> -->
  </div>
</template>

<script setup name="TitleBox">
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  subTitle: {
    type: String,
    default: "",
  },
  className: {
    type: String,
    default: "",
  },
});
</script>

<style lang="less" scoped>
.subTitle {
  font-size: 22px;
  margin-top: 10px;
  color: #5f6368;
}
</style>
