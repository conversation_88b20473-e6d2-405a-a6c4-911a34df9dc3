<template>
  <i v-if="iconFileName.indexOf('el-icon-') === 0" :class="iconFileName" />
  <svg
    v-else
    class="svg-icon"
    :class="{ 'custom-size': width || height }"
    aria-hidden="true"
    v-show="mounted"
  >
    <use :xlink:href="`#${iconFileName}`" />
  </svg>
</template>

<script setup>
import { ref, onMounted } from "vue";

const props = defineProps({
  iconFileName: {
    type: String,
    required: true,
  },
  width: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "",
  },
});

const mounted = ref(false);

onMounted(() => {
  mounted.value = true;
});
</script>

<style scoped lang="less">
.svg-icon {
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentColor;
}

.svg-icon:not(.custom-size) {
  width: 1em;
  height: 1em;
}

.svg-icon.custom-size {
  width: v-bind("width");
  height: v-bind("height");
  line-height: v-bind("height");
}
</style>
