<template>
  <!-- 移动端 后期如果需要移动端适配 可放开看效果 基本已经配置完毕 添加样式即可 -->
  <!-- <div v-if="isMobile" class="mobile-header">
    <NuxtImg preset="webp" src="/images/logo_small.png" class="logo" />
    <div class="menu-list">
      <div class="menu-item">
        <span>公司介紹</span>
      </div>
    </div>
    <div class="lang-list">
      <div class="lang-item">
        <span>EN</span>
      </div>
    </div>
  </div> -->

  <!-- 电脑端 -->
  <div class="AppHeader">
    <div class="pageLogo" @click="goTo('/')">
      <SvgIcon class="logo" iconFileName="logo" width="10em" height="100%" />
    </div>
    <nav class="pageLink">
      <a
        v-for="item in linkList"
        :key="item.page"
        :class="['nav-link', { active: onLinkClick === item.page }]"
        @click="onLinkClickFun(item)"
      >
        {{ $t(`home.header_list.${item.page}`) }}
      </a>
    </nav>
    <div class="pageLang">
      <a
        v-for="item in langList"
        :key="item.lang"
        :class="[
          'lang-link',
          { active: onLangClick === item.lang, disabled: item.disabled },
        ]"
        @click="onLangClickFun(item)"
      >
        {{ item.name }}
      </a>
    </div>
  </div>
</template>

<script setup name="AppHeader">
import { ref, watch } from "vue";
import { goTo } from "../utils/goTo";

import { useDevice } from "../utils/useDevice";
const { isMobile } = useDevice();

const { t, locale, setLocale } = useI18n();
const route = useRoute();

const onLinkClick = ref("home");
const onLangClick = ref("zh-hk");

// 定义数据
const linkList = [
  { name: "公司介紹", path: "/", page: "home" },
  { name: "解決方案", path: "/solutions", page: "solutions" },
  { name: "智能機器人", path: "/robots", page: "robots" },
  { name: "自主無人機", path: "/drones", page: "drones" },
  { name: "AI算法", path: "/algorithms", page: "algorithms" },
  { name: "結構分析", path: "/analysis", page: "analysis" },
  { name: "健康監測", path: "/monitoring", page: "monitoring" },
  { name: "設備定制", path: "/equipment", page: "equipment" },
  { name: "關於我們", path: "/about", page: "about" },
];

const langList = [
  { name: "EN", lang: "en", disabled: true },
  { name: "繁", lang: "zh-hk", disabled: false },
  { name: "简", lang: "zh-cn", disabled: false },
];

const parseCurrentRoute = () => {
  const path = route.path.split("/");
  const pathLast = path[path.length - 1];

  onLinkClick.value = pathLast === "" ? "home" : pathLast;

  const lang = path[1];
  if (["en", "zh-cn", "zh-hk"].includes(lang)) {
    onLangClick.value = lang;
    setLocale(lang);
  } else {
    onLangClick.value = locale.value || "zh-hk";
  }
};

watch(
  () => route.path,
  () => {
    parseCurrentRoute();
  },
  { immediate: true }
);

const onLinkClickFun = async (item) => {
  onLinkClick.value = item.page;
  try {
    let routePath = item.path;
    if (locale.value === "zh-cn") {
      routePath = `/zh-cn${item.path}`;
    } else if (locale.value === "en") {
      routePath = `/en${item.path}`;
    }
    await navigateTo(routePath);
  } catch (error) {
    const homePath =
      locale.value === "zh-cn"
        ? "/zh-cn/"
        : locale.value === "en"
        ? "/en/"
        : "/";
    await navigateTo(homePath);
  }
};

const onLangClickFun = async (item) => {
  if (item.disabled) return;
  onLangClick.value = item.lang;
  setLocale(item.lang);

  const currentPath = route.path;
  let newPath = currentPath;

  if (
    currentPath.startsWith("/en/") ||
    currentPath.startsWith("/zh-cn/") ||
    currentPath.startsWith("/zh-hk/")
  ) {
    newPath = currentPath.substring(currentPath.indexOf("/", 1));
  }

  if (item.lang === "zh-cn") {
    newPath = `/zh-cn${newPath}`;
  } else if (item.lang === "en") {
    newPath = `/en${newPath}`;
  }

  await navigateTo(newPath);
};
</script>

<style lang="less" scoped>
.AppHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 60px;
  height: 80px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .pageLogo {
    width: 200px;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .pageLink {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;

    .nav-link {
      position: relative;
      color: #333;
      font-size: 22px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px 0;

      &:hover {
        color: #1a73e8;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: #1a73e8;
        transform: translateX(-50%);
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }

      &.active {
        color: #1a73e8;

        &::after {
          width: 100%;
        }
      }
    }
  }

  .pageLang {
    display: flex;
    align-items: center;
    gap: 16px;

    .lang-link {
      color: #666;
      font-size: 22px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 4px;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #1a73e8;
        background: rgba(24, 144, 255, 0.1);
      }

      &.active {
        color: #fff;
        background: #1a73e8;

        &:hover {
          background: #40a9ff;
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1650px) {
  .AppHeader {
    padding: 0 30px;

    .pageLink {
      gap: 15px;
      .nav-link {
      }
    }
  }
}

@media (max-width: 1460px) {
  .AppHeader {
    padding: 0 2px;
    .pageLang {
      gap: 8px;
      .lang-link {
        padding: 3px 6px;
      }
    }
    .pageLink {
      gap: 8px;

      .nav-link {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

// 移动端样式
.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 0.2rem;
  height: 80px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  .logo {
    width: 1.5rem;
  }
}
</style>
