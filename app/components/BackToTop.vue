<template>
  <div
    class="back-to-top"
    :class="{ 'back-to-top--show': show }"
    @click="scrollToTop"
  >
    <ArrowUpOutlined />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

const show = ref(false);
const scrollThreshold = 300;
let mainElement: HTMLElement | null = null;

const handleScroll = () => {
  if (mainElement) {
    show.value = mainElement.scrollTop > scrollThreshold;
  }
};

const scrollToTop = () => {
  if (mainElement) {
    mainElement.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
};

onMounted(() => {
  mainElement = document.querySelector(".app-main");
  if (mainElement) {
    mainElement.addEventListener("scroll", handleScroll);
  }
});

onUnmounted(() => {
  if (mainElement) {
    mainElement.removeEventListener("scroll", handleScroll);
  }
});
</script>

<style lang="less" scoped>
.back-to-top {
  position: fixed;
  right: 60px;
  bottom: 60px;
  width: 60px;
  height: 60px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
  font-size: 24px;

  &:hover {
    background-color: #4096ff;
    color: #fff;
    transform: translateY(-2px);
  }

  svg {
    color: #020107;
  }

  &--show {
    opacity: 1;
    visibility: visible;
  }
}
</style>
