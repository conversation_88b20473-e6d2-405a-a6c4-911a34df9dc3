<template>
  <a-config-provider :theme="theme">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </a-config-provider>
</template>

<script setup>
import { theme } from "./styles/antd";
import { createSvgSprite } from "./utils/svgSprite";

// 在客户端加载SVG Sprite
onMounted(() => {
  createSvgSprite();
});

// 全局样式和配置
useHead({
  titleTemplate: "%s - TechProClub", // 页面标题
  htmlAttrs: {
    lang: "zh-CN", // 页面语言
  },
  meta: [
    { charset: "utf-8" }, // 字符集
    { name: "viewport", content: "width=device-width, initial-scale=1" }, // 视口
  ],
  link: [{ rel: "icon", type: "image/x-icon", href: "/favicon.ico" }], // 图标
});
</script>

<style>
/* 如果需要覆盖组件样式，可以在这里添加 */
.ant-btn {
  /* 按钮样式覆盖 */
}

.ant-input {
  /* 输入框样式覆盖 */
}
</style>
